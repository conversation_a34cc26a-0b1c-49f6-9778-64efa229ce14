#!/bin/bash

# FBA货件明细数据同步脚本
# 实现两阶段数据同步：领星API → ERP数据库 → Logistics数据库

# 配置参数
token='01c3ddaa-3e5e-11ef-b635-9c2dcd695fd0'
qwUrl='https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_webhook_key'

echo "开始FBA货件明细数据同步..."
start_time=$(date +"%Y-%m-%d %H:%M:%S")

# 第一阶段：从领星API同步到ERP数据库
echo "=========================================="
echo "阶段1: 同步领星API数据到ERP数据库"
echo "=========================================="

api_start_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "开始时间: $api_start_time"

while true; do
    echo "调用领星API同步接口..."
    api_response=$(curl -s -X POST -d "token=$token&batch_size=100" 'http://oa.ywx.com/task/lingXingApi/synFbaInboundShipment')
    echo "API响应: $api_response"
    
    # 从API响应中提取code字段的值
    api_code=$(echo "$api_response" | grep -oP '"code":\K-?\d+')
    # 从API响应中提取msg字段的值
    api_msg=$(echo "$api_response" | grep -oP '"message"\s*:\s*"\K[^"]+')
    
    echo "API同步结果: code=$api_code, msg=$api_msg"
    
    if [ "$api_code" -eq 2 ]; then
        api_end_time=$(date +"%Y-%m-%d %H:%M:%S")
        echo "领星API数据同步完成，结束时间: $api_end_time"
        break
    elif [ "$api_code" -eq -1 ]; then
        echo "API同步失败，终止处理"
        # 发送失败通知
        curl -s "$qwUrl" -H "Content-Type: application/json" -d "{\"msgtype\":\"text\",\"text\":{\"content\":\"❌ FBA货件明细API同步失败\\n📊 错误信息: $api_msg\\n⏰ 失败时间: $api_end_time\"}}"
        exit 1
    else
        echo "继续同步领星数据，code=$api_code"
        sleep 2
    fi
done

# 第二阶段：从ERP数据库转换到Logistics数据库
echo "=========================================="
echo "阶段2: 转换数据到Logistics数据库"
echo "=========================================="

transform_start_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "开始时间: $transform_start_time"

echo "调用数据转换接口..."
transform_response=$(curl -s -X POST -d "token=$token&batch_size=100" 'http://oa.ywx.com/task/logistics/transformFbaInboundShipmentData')
echo "转换响应: $transform_response"

# 从转换响应中提取code字段的值
transform_code=$(echo "$transform_response" | grep -oP '"code":\K-?\d+')
# 从转换响应中提取msg字段的值
transform_msg=$(echo "$transform_response" | grep -oP '"message"\s*:\s*"\K[^"]+')

transform_end_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "转换结果: code=$transform_code, msg=$transform_msg"
echo "结束时间: $transform_end_time"

# 总结和通知
echo "=========================================="
echo "同步任务总结"
echo "=========================================="

end_time=$(date +"%Y-%m-%d %H:%M:%S")
echo "任务开始时间: $start_time"
echo "任务结束时间: $end_time"
echo "阶段1结果: API同步 code=$api_code"
echo "阶段2结果: 数据转换 code=$transform_code"

# 检查整体结果并发送通知
if [ "$api_code" -eq 2 ] && [ "$transform_code" -eq 2 ]; then
    echo "✅ FBA货件明细数据同步完全成功"
    
    # 发送成功通知
    curl -s "$qwUrl" -H "Content-Type: application/json" -d "{
        \"msgtype\": \"text\",
        \"text\": {
            \"content\": \"✅ FBA货件明细数据同步完成\\n📊 API同步: $api_msg\\n📈 数据转换: $transform_msg\\n⏰ 完成时间: $end_time\"
        }
    }"
    
    echo "成功通知已发送"
    exit 0
    
elif [ "$api_code" -eq 2 ] && [ "$transform_code" -ne 2 ]; then
    echo "⚠️ API同步成功，但数据转换失败"
    
    # 发送部分失败通知
    curl -s "$qwUrl" -H "Content-Type: application/json" -d "{
        \"msgtype\": \"text\",
        \"text\": {
            \"content\": \"⚠️ FBA货件明细数据同步部分失败\\n✅ API同步: 成功\\n❌ 数据转换: $transform_msg\\n⏰ 失败时间: $end_time\"
        }
    }"
    
    echo "部分失败通知已发送"
    exit 1
    
else
    echo "❌ FBA货件明细数据同步失败"
    
    # 发送完全失败通知
    curl -s "$qwUrl" -H "Content-Type: application/json" -d "{
        \"msgtype\": \"text\",
        \"text\": {
            \"content\": \"❌ FBA货件明细数据同步失败\\n📊 API同步: $api_msg\\n📈 数据转换: $transform_msg\\n⏰ 失败时间: $end_time\"
        }
    }"
    
    echo "失败通知已发送"
    exit 1
fi
