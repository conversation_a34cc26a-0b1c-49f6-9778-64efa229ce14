<?php

namespace plugins\logistics\models;

use core\lib\db\dbErpMysql;

/**
 * FBA货件原始数据模型
 * 处理领星API原始数据的存储和查询
 */
class fbaInboundShipmentRawModel
{
    private $db;

    public function __construct()
    {
        $this->db = dbErpMysql::getInstance();
    }

    /**
     * 批量保存领星API原始数据
     * @param array $apiData 领星API返回的数据
     * @return bool
     */
    public function batchSaveApiData($apiData)
    {
        if (empty($apiData) || !isset($apiData['list'])) {
            return false;
        }

        try {
            $this->db->beginTransaction();

            foreach ($apiData['list'] as $shipment) {
                $this->saveShipmentData($shipment);
            }

            $this->db->commit();
            return true;

        } catch (\Exception $e) {
            $this->db->rollBack();
            error_log("批量保存FBA货件原始数据失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 保存单个货件数据
     * @param array $shipment 货件数据
     * @return bool
     */
    private function saveShipmentData($shipment)
    {
        $shipmentSn = $shipment['shipment_sn'] ?? '';
        if (empty($shipmentSn)) {
            return false;
        }

        // 检查是否已存在
        $existing = $this->getByShipmentSn($shipmentSn);

        $data = $this->mapApiDataToRawData($shipment);

        if ($existing) {
            // 更新现有记录
            return $this->updateRawData($existing['id'], $data);
        } else {
            // 插入新记录
            return $this->insertRawData($data);
        }
    }

    /**
     * 映射API数据到原始数据表字段
     * @param array $shipment API货件数据
     * @return array
     */
    private function mapApiDataToRawData($shipment)
    {
        return [
            'shipment_sn' => $shipment['shipment_sn'] ?? '',
            'shipment_data' => json_encode($shipment, JSON_UNESCAPED_UNICODE),
            'relate_list' => json_encode($shipment['relate_list'] ?? [], JSON_UNESCAPED_UNICODE),
            'logistics_data' => json_encode($shipment['logistics'] ?? [], JSON_UNESCAPED_UNICODE),
            
            // API原始字段
            'original_id' => (int)($shipment['id'] ?? 0),
            'status' => (int)($shipment['status'] ?? 0),
            'wname' => $shipment['wname'] ?? '',
            'wid' => (int)($shipment['wid'] ?? 0),
            'create_time' => $shipment['create_time'] ?? '',
            'gmt_create' => $shipment['gmt_create'] ?? '',
            'shipment_time' => $shipment['shipment_time'] ?? '',
            'shipment_time_second' => $shipment['shipment_time_second'] ?? '',
            'logistics_provider_id' => $shipment['logistics_provider_id'] ?? '',
            'logistics_provider_name' => $shipment['logistics_provider_name'] ?? '',
            'logistics_channel_name' => $shipment['logistics_channel_name'] ?? '',
            'method_id' => $shipment['method_id'] ?? '',
            'method_name' => $shipment['method_name'] ?? '',
            'expected_arrival_date' => $shipment['expected_arrival_date'] ?? '',
            'etd_date' => $shipment['etd_date'] ?? '',
            'eta_date' => $shipment['eta_date'] ?? '',
            'delivery_date' => $shipment['delivery_date'] ?? '',
            'create_user' => $shipment['create_user'] ?? '',
            'shipment_user' => $shipment['shipment_user'] ?? '',
            'remark' => $shipment['remark'] ?? '',
            'destination_fulfillment_center_id' => $shipment['destination_fulfillment_center_id'] ?? '',
            'status_name' => $shipment['status_name'] ?? '',
            'head_fee_type' => (int)($shipment['head_fee_type'] ?? 0),
            'head_fee_type_name' => $shipment['head_fee_type_name'] ?? '',
            'is_pick' => (int)($shipment['is_pick'] ?? 0),
            'is_print' => (int)($shipment['is_print'] ?? 0),
            'pick_time' => $shipment['pick_time'] ?? '',
            'print_num' => (int)($shipment['print_num'] ?? 0),
            'file_id' => $shipment['file_id'] ?? '',
            'is_return_stock' => (int)($shipment['is_return_stock'] ?? 0),
            'pay_status' => (int)($shipment['pay_status'] ?? 0),
            'audit_status' => (int)($shipment['audit_status'] ?? 0),
            'is_exist_declaration' => (int)($shipment['is_exist_declaration'] ?? 0),
            'is_exist_clearance' => (int)($shipment['is_exist_clearance'] ?? 0),
            'third_party_order_mode' => (int)($shipment['third_party_order_mode'] ?? 0),
            'third_party_order_status' => (int)($shipment['third_party_order_status'] ?? 0),
            'vat_code' => $shipment['vat_code'] ?? '',
            'is_custom_shipment_time' => (int)($shipment['is_custom_shipment_time'] ?? 0),
            'update_time' => $shipment['update_time'] ?? '',
            'is_delete' => (int)($shipment['is_delete'] ?? 0),
            
            // 系统字段
            'sync_date' => date('Y-m-d'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_deleted' => 0
        ];
    }

    /**
     * 根据发货单号获取原始数据
     * @param string $shipmentSn 发货单号
     * @return array|false
     */
    public function getByShipmentSn($shipmentSn)
    {
        $sql = "SELECT * FROM lingxing_fba_inbound_shipment 
                WHERE shipment_sn = :shipment_sn AND is_deleted = 0";
        
        return $this->db->query($sql, ['shipment_sn' => $shipmentSn]);
    }

    /**
     * 获取待转换的原始数据
     * @param int $offset 偏移量
     * @param int $limit 限制数量
     * @param string $syncDate 同步日期
     * @return array
     */
    public function getUnprocessedData($offset = 0, $limit = 100, $syncDate = '')
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }

        $sql = "SELECT * FROM lingxing_fba_inbound_shipment 
                WHERE sync_date = :sync_date AND is_deleted = 0 
                ORDER BY id ASC 
                LIMIT :offset, :limit";
        
        return $this->db->queryAll($sql, [
            'sync_date' => $syncDate,
            'offset' => $offset,
            'limit' => $limit
        ]);
    }

    /**
     * 获取待转换数据总数
     * @param string $syncDate 同步日期
     * @return int
     */
    public function getUnprocessedCount($syncDate = '')
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }

        $sql = "SELECT COUNT(*) as total FROM lingxing_fba_inbound_shipment 
                WHERE sync_date = :sync_date AND is_deleted = 0";
        
        $result = $this->db->query($sql, ['sync_date' => $syncDate]);
        return $result ? (int)$result['total'] : 0;
    }

    /**
     * 插入原始数据
     * @param array $data 数据
     * @return bool
     */
    private function insertRawData($data)
    {
        return $this->db->table('lingxing_fba_inbound_shipment')->insert($data);
    }

    /**
     * 更新原始数据
     * @param int $id 记录ID
     * @param array $data 数据
     * @return bool
     */
    private function updateRawData($id, $data)
    {
        unset($data['created_at']); // 保持原创建时间
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->table('lingxing_fba_inbound_shipment')
            ->where('id = :id', ['id' => $id])
            ->update($data);
    }

    /**
     * 获取原始数据统计
     * @param string $syncDate 同步日期
     * @return array
     */
    public function getRawDataStats($syncDate = '')
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }

        $sql = "SELECT 
                    COUNT(*) as total_count,
                    COUNT(DISTINCT wid) as warehouse_count,
                    COUNT(DISTINCT status) as status_count,
                    MIN(created_at) as first_sync_time,
                    MAX(updated_at) as last_update_time
                FROM lingxing_fba_inbound_shipment 
                WHERE sync_date = :sync_date AND is_deleted = 0";
        
        return $this->db->query($sql, ['sync_date' => $syncDate]) ?: [];
    }

    /**
     * 清理指定日期的原始数据
     * @param string $syncDate 同步日期
     * @return bool
     */
    public function clearRawDataByDate($syncDate)
    {
        return $this->db->table('lingxing_fba_inbound_shipment')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }
}
