<?php

namespace plugins\logistics\models;

use core\lib\db\dbLMysql;

/**
 * FBA货件明细业务数据模型
 * 处理业务数据的查询、编辑、导入导出
 */
class fbaInboundShipmentDetailModel
{
    private $db;

    public function __construct()
    {
        $this->db = dbLMysql::getInstance();
    }

    /**
     * 从原始数据转换并保存到业务表
     * @param array $rawDataList 原始数据列表
     * @return array 处理结果
     */
    public function syncFromRawData($rawDataList)
    {
        $result = [
            'success_count' => 0,
            'error_count' => 0,
            'errors' => []
        ];

        try {
            $this->db->beginTransaction();

            foreach ($rawDataList as $rawData) {
                try {
                    $this->processShipmentData($rawData);
                    $result['success_count']++;
                } catch (\Exception $e) {
                    $result['error_count']++;
                    $result['errors'][] = "发货单号{$rawData['shipment_sn']}处理失败: " . $e->getMessage();
                }
            }

            $this->db->commit();

        } catch (\Exception $e) {
            $this->db->rollBack();
            throw $e;
        }

        return $result;
    }

    /**
     * 处理单个货件数据
     * @param array $rawData 原始数据
     */
    private function processShipmentData($rawData)
    {
        $shipmentSn = $rawData['shipment_sn'];
        $relateList = json_decode($rawData['relate_list'], true) ?: [];

        foreach ($relateList as $relate) {
            $relateId = $relate['id'] ?? 0;
            if (empty($relateId)) {
                continue;
            }

            // 检查是否已存在
            $existing = $this->getByShipmentSnAndRelateId($shipmentSn, $relateId);

            $detailData = $this->mapRawDataToDetailData($rawData, $relate);

            if ($existing) {
                // 更新：保留可编辑字段
                $this->updateDetailData($existing['id'], $detailData);
            } else {
                // 插入新记录
                $this->insertDetailData($detailData);
            }
        }
    }

    /**
     * 映射原始数据到明细数据
     * @param array $rawData 原始数据
     * @param array $relate 关联明细数据
     * @return array
     */
    private function mapRawDataToDetailData($rawData, $relate)
    {
        // 解析物流数据获取跟踪单号
        $logisticsData = json_decode($rawData['logistics_data'], true) ?: [];
        $trackingNumber = '';
        if (!empty($logisticsData)) {
            $trackingNumber = $logisticsData['tracking_no'] ?? 
                             $logisticsData['replace_tracking_number'] ?? 
                             $logisticsData['tracking_number'] ?? '';
        }

        return [
            'shipment_sn' => $rawData['shipment_sn'],
            'relate_id' => (int)($relate['id'] ?? 0),
            
            // 24个展示字段
            'warehouse_name' => $rawData['wname'] ?? '',
            'plan_date' => $this->parseDate($rawData['create_time']),
            'group_name' => '', // 需要根据业务规则设置
            'responsible_person' => $rawData['create_user'] ?? '',
            'site' => $relate['sname'] ?? '',
            'transport_method' => $rawData['method_name'] ?? '',
            'product_name' => $relate['product_name'] ?? '',
            'asin' => $relate['asin'] ?? '',
            'fnsku' => $relate['fnsku'] ?? '',
            'plan_quantity' => (int)($relate['num'] ?? 0),
            'box_count' => 0, // 需要根据业务规则计算
            'auxiliary_materials' => '', // 需要根据业务规则设置
            'shipment_code' => $relate['shipment_id'] ?? '',
            'delivery_address' => '', // 需要根据物流中心编码获取
            'tracking_number' => $trackingNumber,
            'ship_date' => $this->parseDate($rawData['shipment_time']),
            'shipment_status' => $relate['shipment_status'] ?? '',
            'delivery_time_slot' => '', // 需要根据业务规则设置
            'received_quantity' => (int)($relate['quantity_shipped'] ?? 0),
            'difference' => (int)($relate['diff_num'] ?? 0),
            'transparent_label' => '', // 可编辑字段，初始为空
            'is_label_changed' => 0, // 可编辑字段，初始为否
            'remark' => $relate['remark'] ?? '', // 可编辑字段
            'remark2' => '', // 可编辑字段，初始为空
            
            // 筛选字段
            'country' => $relate['nation'] ?? '',
            'shop_code' => $this->extractShopCode($relate['sname'] ?? ''),
            'sku' => $relate['sku'] ?? '',
            'warehouse_code' => $relate['destination_fulfillment_center_id'] ?? '',
            
            // 原始数据关联字段
            'original_shipment_id' => $rawData['id'],
            'original_relate_detail_id' => $relate['id'] ?? 0,
            'shipment_id' => $relate['shipment_id'] ?? '',
            'destination_fulfillment_center_id' => $relate['destination_fulfillment_center_id'] ?? '',
            'quantity_shipped' => (int)($relate['quantity_shipped'] ?? 0),
            'num' => (int)($relate['num'] ?? 0),
            'apply_num' => (int)($relate['apply_num'] ?? 0),
            'product_id' => (int)($relate['product_id'] ?? 0),
            'parent_asin' => $relate['parent_asin'] ?? '',
            'msku' => $relate['msku'] ?? '',
            'fulfillment_network_sku' => $relate['fulfillment_network_sku'] ?? '',
            'pic_url' => $relate['pic_url'] ?? '',
            'packing_type' => (int)($relate['packing_type'] ?? 0),
            'packing_type_name' => $relate['packing_type_name'] ?? '',
            'is_combo' => (int)($relate['is_combo'] ?? 0),
            'create_by_mws' => (int)($relate['create_by_mws'] ?? 0),
            'product_valid_num' => (int)($relate['product_valid_num'] ?? 0),
            'product_qc_num' => (int)($relate['product_qc_num'] ?? 0),
            'diff_num' => (int)($relate['diff_num'] ?? 0),
            'sta_shipment_id' => $relate['sta_shipment_id'] ?? '',
            'is_sta' => $relate['is_sta'] ?? '0',
            'sta_inbound_plan_id' => $relate['sta_inbound_plan_id'] ?? '',
            
            // 系统字段
            'sync_date' => date('Y-m-d'),
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s'),
            'is_deleted' => 0
        ];
    }

    /**
     * 解析日期字符串
     * @param string $dateStr 日期字符串
     * @return string|null
     */
    private function parseDate($dateStr)
    {
        if (empty($dateStr)) {
            return null;
        }
        
        $timestamp = strtotime($dateStr);
        return $timestamp ? date('Y-m-d', $timestamp) : null;
    }

    /**
     * 从店铺名称中提取店铺代码
     * @param string $shopName 店铺名称
     * @return string
     */
    private function extractShopCode($shopName)
    {
        // 简单的店铺代码提取逻辑，可根据实际情况调整
        if (preg_match('/^([^:：]+)/', $shopName, $matches)) {
            return trim($matches[1]);
        }
        return $shopName;
    }

    /**
     * 根据发货单号和关联ID获取明细数据
     * @param string $shipmentSn 发货单号
     * @param int $relateId 关联ID
     * @return array|false
     */
    public function getByShipmentSnAndRelateId($shipmentSn, $relateId)
    {
        $sql = "SELECT * FROM fba_inbound_shipment_detail 
                WHERE shipment_sn = :shipment_sn AND relate_id = :relate_id AND is_deleted = 0";
        
        return $this->db->query($sql, [
            'shipment_sn' => $shipmentSn,
            'relate_id' => $relateId
        ]);
    }

    /**
     * 插入明细数据
     * @param array $data 明细数据
     * @return bool
     */
    private function insertDetailData($data)
    {
        return $this->db->table('fba_inbound_shipment_detail')->insert($data);
    }

    /**
     * 更新明细数据（保留可编辑字段）
     * @param int $id 记录ID
     * @param array $data 明细数据
     * @return bool
     */
    private function updateDetailData($id, $data)
    {
        // 移除可编辑字段，避免覆盖用户编辑的内容
        $editableFields = ['tracking_number', 'transparent_label', 'is_label_changed', 'remark', 'remark2'];
        foreach ($editableFields as $field) {
            unset($data[$field]);
        }
        
        unset($data['created_at']); // 保持原创建时间
        $data['updated_at'] = date('Y-m-d H:i:s');
        
        return $this->db->table('fba_inbound_shipment_detail')
            ->where('id = :id', ['id' => $id])
            ->update($data);
    }

    /**
     * 获取明细数据列表（支持筛选和分页）
     * @param array $params 查询参数
     * @return array
     */
    public function getDetailList($params = [])
    {
        $page = (int)($params['page'] ?? 1);
        $pageSize = (int)($params['page_size'] ?? 20);
        $offset = ($page - 1) * $pageSize;

        $where = "is_deleted = 0";
        $whereParams = [];

        // 构建筛选条件
        $this->buildWhereConditions($params, $where, $whereParams);

        // 获取总数
        $countSql = "SELECT COUNT(*) as total FROM fba_inbound_shipment_detail WHERE $where";
        $totalResult = $this->db->query($countSql, $whereParams);
        $total = $totalResult ? (int)$totalResult['total'] : 0;

        // 获取列表数据
        $listSql = "SELECT * FROM fba_inbound_shipment_detail 
                    WHERE $where 
                    ORDER BY created_at DESC 
                    LIMIT :offset, :page_size";
        
        $whereParams['offset'] = $offset;
        $whereParams['page_size'] = $pageSize;
        
        $list = $this->db->queryAll($listSql, $whereParams);

        return [
            'list' => $list,
            'total' => $total,
            'page' => $page,
            'page_size' => $pageSize,
            'total_pages' => ceil($total / $pageSize)
        ];
    }

    /**
     * 构建查询条件
     * @param array $params 查询参数
     * @param string &$where WHERE条件
     * @param array &$whereParams 参数数组
     */
    private function buildWhereConditions($params, &$where, &$whereParams)
    {
        // 国家筛选
        if (!empty($params['country'])) {
            $where .= " AND country = :country";
            $whereParams['country'] = $params['country'];
        }

        // 计划时间范围筛选
        if (!empty($params['plan_date_start'])) {
            $where .= " AND plan_date >= :plan_date_start";
            $whereParams['plan_date_start'] = $params['plan_date_start'];
        }
        if (!empty($params['plan_date_end'])) {
            $where .= " AND plan_date <= :plan_date_end";
            $whereParams['plan_date_end'] = $params['plan_date_end'];
        }

        // 仓库筛选
        if (!empty($params['warehouse_name'])) {
            $where .= " AND warehouse_name LIKE :warehouse_name";
            $whereParams['warehouse_name'] = '%' . $params['warehouse_name'] . '%';
        }

        // 运输方式筛选
        if (!empty($params['transport_method'])) {
            $where .= " AND transport_method LIKE :transport_method";
            $whereParams['transport_method'] = '%' . $params['transport_method'] . '%';
        }

        // 产品名称筛选
        if (!empty($params['product_name'])) {
            $where .= " AND product_name LIKE :product_name";
            $whereParams['product_name'] = '%' . $params['product_name'] . '%';
        }

        // 店铺代码筛选
        if (!empty($params['shop_code'])) {
            $where .= " AND shop_code = :shop_code";
            $whereParams['shop_code'] = $params['shop_code'];
        }

        // FNSKU筛选
        if (!empty($params['fnsku'])) {
            $where .= " AND fnsku = :fnsku";
            $whereParams['fnsku'] = $params['fnsku'];
        }

        // 入仓编号筛选
        if (!empty($params['warehouse_code'])) {
            $where .= " AND warehouse_code = :warehouse_code";
            $whereParams['warehouse_code'] = $params['warehouse_code'];
        }

        // 备注筛选
        if (!empty($params['remark'])) {
            $where .= " AND (remark LIKE :remark OR remark2 LIKE :remark2)";
            $whereParams['remark'] = '%' . $params['remark'] . '%';
            $whereParams['remark2'] = '%' . $params['remark'] . '%';
        }
    }

    /**
     * 更新可编辑字段
     * @param string $shipmentCode 货件编码
     * @param array $editableData 可编辑数据
     * @return bool
     */
    public function updateEditableFields($shipmentCode, $editableData)
    {
        $allowedFields = ['tracking_number', 'transparent_label', 'is_label_changed', 'remark', 'remark2'];
        $updateData = [];

        foreach ($allowedFields as $field) {
            if (isset($editableData[$field])) {
                $updateData[$field] = $editableData[$field];
            }
        }

        if (empty($updateData)) {
            return false;
        }

        $updateData['updated_at'] = date('Y-m-d H:i:s');

        return $this->db->table('fba_inbound_shipment_detail')
            ->where('shipment_code = :shipment_code AND is_deleted = 0', ['shipment_code' => $shipmentCode])
            ->update($updateData);
    }

    /**
     * 清理指定日期的明细数据
     * @param string $syncDate 同步日期
     * @return bool
     */
    public function clearDetailsByDate($syncDate)
    {
        return $this->db->table('fba_inbound_shipment_detail')
            ->where('sync_date = :sync_date', ['sync_date' => $syncDate])
            ->update(['is_deleted' => 1, 'updated_at' => date('Y-m-d H:i:s')]);
    }

    /**
     * 获取明细数据统计
     * @param string $syncDate 同步日期
     * @return array
     */
    public function getDetailStats($syncDate = '')
    {
        if (empty($syncDate)) {
            $syncDate = date('Y-m-d');
        }

        $sql = "SELECT 
                    COUNT(*) as total_count,
                    COUNT(DISTINCT shipment_sn) as shipment_count,
                    COUNT(DISTINCT country) as country_count,
                    SUM(plan_quantity) as total_quantity,
                    COUNT(DISTINCT warehouse_name) as warehouse_count
                FROM fba_inbound_shipment_detail 
                WHERE sync_date = :sync_date AND is_deleted = 0";
        
        return $this->db->query($sql, ['sync_date' => $syncDate]) ?: [];
    }
}
